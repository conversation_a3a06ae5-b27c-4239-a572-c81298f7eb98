#!/usr/bin/env python3
"""
Test simple del organizador - versión mínima para verificar funcionamiento
"""

import os
import shutil
from pathlib import Path
import json
from datetime import datetime

def crear_archivos_test():
    """Crea archivos de prueba"""
    test_dir = Path("test_escritorio")
    
    # Limpiar directorio si existe
    if test_dir.exists():
        shutil.rmtree(test_dir)
    
    test_dir.mkdir()
    
    # Crear archivos de prueba
    archivos = [
        ("documento.txt", "Este es un documento de texto importante"),
        ("imagen.jpg", ""),
        ("video.mp4", ""),
        ("codigo.py", "print('Hola mundo')"),
        ("datos.csv", "nombre,edad\nJuan,25\nMaria,30"),
        ("archivo.zip", ""),
        ("presentacion.pptx", ""),
        ("musica.mp3", ""),
        ("desconocido.xyz", "archivo sin categoria")
    ]
    
    print("📁 Creando archivos de prueba...")
    for nombre, contenido in archivos:
        archivo_path = test_dir / nombre
        with open(archivo_path, 'w', encoding='utf-8') as f:
            f.write(contenido)
        print(f"   ✓ {nombre}")
    
    return test_dir

def organizar_archivos(directorio):
    """Organiza los archivos por categorías"""
    categorias = {
        'Documentos': ['.txt', '.pdf', '.doc', '.docx'],
        'Imagenes': ['.jpg', '.jpeg', '.png', '.gif'],
        'Videos': ['.mp4', '.avi', '.mkv', '.mov'],
        'Audio': ['.mp3', '.wav', '.flac'],
        'Programacion': ['.py', '.js', '.html', '.css'],
        'Datos': ['.csv', '.xlsx', '.xls'],
        'Comprimidos': ['.zip', '.rar', '.7z'],
        'Presentaciones': ['.pptx', '.ppt']
    }
    
    archivos_movidos = {}
    
    print(f"\n🚀 Organizando archivos en {directorio}...")
    
    # Obtener todos los archivos
    archivos = [f for f in directorio.iterdir() if f.is_file()]
    
    for archivo in archivos:
        extension = archivo.suffix.lower()
        categoria_encontrada = None
        
        # Buscar categoría
        for categoria, extensiones in categorias.items():
            if extension in extensiones:
                categoria_encontrada = categoria
                break
        
        if not categoria_encontrada:
            categoria_encontrada = 'Sin_Categoria'
        
        print(f"📄 {archivo.name} → {categoria_encontrada}")
        
        # Crear carpeta si no existe
        carpeta_categoria = directorio / categoria_encontrada
        if not carpeta_categoria.exists():
            carpeta_categoria.mkdir()
            print(f"   📁 Carpeta creada: {categoria_encontrada}")
        
        # Mover archivo
        destino = carpeta_categoria / archivo.name
        try:
            shutil.move(str(archivo), str(destino))
            
            if categoria_encontrada not in archivos_movidos:
                archivos_movidos[categoria_encontrada] = []
            archivos_movidos[categoria_encontrada].append(archivo.name)
            
            print(f"   ✅ Movido exitosamente")
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    return archivos_movidos

def mostrar_estructura(directorio):
    """Muestra la estructura final"""
    print(f"\n📁 ESTRUCTURA FINAL DE {directorio.name}:")
    print("=" * 40)
    
    for item in sorted(directorio.iterdir()):
        if item.is_dir():
            print(f"📂 {item.name}/")
            archivos = list(item.iterdir())
            for archivo in sorted(archivos):
                print(f"   📄 {archivo.name}")
        else:
            print(f"📄 {item.name}")

def generar_informe(directorio, archivos_movidos):
    """Genera informe de la organización"""
    informe = {
        'fecha': datetime.now().isoformat(),
        'directorio': str(directorio),
        'archivos_organizados': archivos_movidos,
        'total_archivos': sum(len(archivos) for archivos in archivos_movidos.values()),
        'categorias_creadas': list(archivos_movidos.keys())
    }
    
    informe_path = directorio / "informe_organizacion.json"
    with open(informe_path, 'w', encoding='utf-8') as f:
        json.dump(informe, f, indent=2, ensure_ascii=False)
    
    print(f"\n📋 INFORME GENERADO:")
    print(f"   📄 Total archivos organizados: {informe['total_archivos']}")
    print(f"   📁 Categorías creadas: {len(informe['categorias_creadas'])}")
    for categoria, archivos in archivos_movidos.items():
        print(f"      • {categoria}: {len(archivos)} archivos")
    print(f"   💾 Informe guardado en: {informe_path.name}")

def main():
    """Función principal"""
    print("🧪 TEST DEL ORGANIZADOR DE ESCRITORIO")
    print("=" * 50)
    
    try:
        # Paso 1: Crear archivos de prueba
        directorio_test = crear_archivos_test()
        
        # Paso 2: Organizar archivos
        archivos_movidos = organizar_archivos(directorio_test)
        
        # Paso 3: Mostrar estructura
        mostrar_estructura(directorio_test)
        
        # Paso 4: Generar informe
        generar_informe(directorio_test, archivos_movidos)
        
        print("\n🎉 ¡Test completado exitosamente!")
        print(f"   📁 Revisa la carpeta '{directorio_test.name}' para ver los resultados")
        
    except Exception as e:
        print(f"❌ Error durante el test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

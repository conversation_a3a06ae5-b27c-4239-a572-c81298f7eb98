@echo off
chcp 65001 >nul
title Demo del Organizador de Escritorio

echo.
echo 🎬 DEMOSTRACIÓN DEL ORGANIZADOR DE ESCRITORIO
echo =============================================
echo.
echo Esta demostración te mostrará cómo funciona el organizador
echo creando archivos de ejemplo y organizándolos automáticamente.
echo.
echo ⚠️  NOTA: Esta demo funciona sin instalar dependencias adicionales
echo.

REM Verificar si Python está instalado
echo 🔍 Verificando Python...

REM Intentar con python
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Python encontrado
    set PYTHON_CMD=python
    goto :run_demo
)

REM Intentar con py
py --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Python encontrado
    set PYTHON_CMD=py
    goto :run_demo
)

REM Intentar con python3
python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Python encontrado
    set PYTHON_CMD=python3
    goto :run_demo
)

REM Python no encontrado
echo.
echo ❌ Python no encontrado
echo.
echo 🐍 Para ejecutar esta demostración necesitas Python instalado.
echo.
echo 📥 OPCIONES RÁPIDAS:
echo.
echo 1. 🏪 Microsoft Store: Busca "Python" e instálalo
echo 2. 🌐 Web oficial: https://www.python.org/downloads/
echo.
echo ¿Quieres abrir Microsoft Store para instalar Python? (S/N)
set /p choice="Tu elección: "
if /i "%choice%"=="s" (
    start ms-windows-store://search/?query=python
    echo.
    echo 📱 Microsoft Store abierto. Instala Python y ejecuta este script nuevamente.
)
echo.
pause
exit /b 1

:run_demo
echo.
echo 🚀 Iniciando demostración...
echo.

REM Verificar si el script de demo existe
if not exist "demo_organizador.py" (
    echo ❌ Error: No se encontró demo_organizador.py
    echo.
    pause
    exit /b 1
)

REM Ejecutar la demostración
%PYTHON_CMD% demo_organizador.py

echo.
echo ✨ Demostración completada
echo.
pause

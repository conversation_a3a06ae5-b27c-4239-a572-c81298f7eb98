# 🏠 Organizador Inteligente de Escritorio

Un script de Python que analiza y organiza automáticamente todos los archivos de tu escritorio por categorías, utilizando análisis de contenido inteligente.

## 🌟 Características

- **Análisis Inteligente**: Examina el contenido real de los archivos, no solo las extensiones
- **Múltiples Formatos**: Soporta documentos, imágenes, videos, audio, código y más
- **Instalación Automática**: Instala todas las dependencias necesarias automáticamente
- **Informe Detallado**: Genera un reporte completo de la organización realizada
- **Seguro**: Solo mueve archivos, nunca los elimina
- **Personalizable**: Permite especificar carpetas personalizadas

## 📋 Categorías de Organización

El organizador clasifica los archivos en las siguientes categorías:

- 📄 **Documentos**: PDFs, Word, texto, etc.
- 🖼️ **Imágenes**: JPG, PNG, GIF, SVG, etc.
- 🎬 **Videos**: MP4, AVI, MKV, MOV, etc.
- 🎵 **Audio**: MP3, WAV, FLAC, AAC, etc.
- 💻 **Programación**: Python, JavaScript, HTML, CSS, etc.
- 📦 **Archivos Comprimidos**: ZIP, RAR, 7Z, etc.
- 📊 **Hojas de Cálculo**: Excel, CSV, ODS, etc.
- 📽️ **Presentaciones**: PowerPoint, ODP, etc.

## 🚀 Instalación Rápida

### Opción 1: Instalación Automática
```bash
python instalar_organizador.py
```

### Opción 2: Instalación Manual
```bash
pip install python-magic-bin chardet pillow PyPDF2 python-docx openpyxl mutagen
```

## 💻 Uso

### Método 1: Script Ejecutable (Windows)
1. Ejecuta `instalar_organizador.py` primero
2. Haz doble clic en `ejecutar_organizador.bat`
3. ¡Listo! Tu escritorio se organizará automáticamente

### Método 2: Línea de Comandos
```bash
# Organizar escritorio por defecto
python organizador_escritorio.py

# Organizar carpeta específica
python organizador_escritorio.py "C:\ruta\a\carpeta"
```

## 🔍 Cómo Funciona

1. **Escaneo**: Encuentra todos los archivos en la carpeta objetivo
2. **Análisis**: Examina cada archivo usando múltiples métodos:
   - Extensión del archivo
   - Tipo MIME
   - Contenido del archivo (para texto, PDFs, Word, etc.)
   - Metadatos (para imágenes, audio, etc.)
   - Nombre del archivo
3. **Clasificación**: Asigna una puntuación a cada categoría posible
4. **Organización**: Mueve los archivos a carpetas apropiadas
5. **Reporte**: Genera un informe detallado en JSON

## 📊 Ejemplo de Análisis

```
🔍 Analizando: documento_importante.pdf
   • Extensión: .pdf → +15 puntos para Documentos
   • Contenido: "informe anual" → +5 puntos para Documentos
   • Tipo MIME: application/pdf → +10 puntos para Documentos
   • Total: 30 puntos → Categoría: Documentos ✅

📄 Movido: documento_importante.pdf → Documentos
```

## 📋 Informe Generado

Al finalizar, el script genera un informe que incluye:

- ✅ Número de archivos procesados
- 📁 Carpetas creadas
- 📊 Distribución por categorías
- ❓ Archivos sin categoría definida
- ⚠️ Errores encontrados (si los hay)

## 🛡️ Seguridad

- **No elimina archivos**: Solo los mueve a carpetas organizadas
- **Manejo de duplicados**: Si existe un archivo con el mismo nombre, agrega un número
- **Respaldo recomendado**: Siempre haz una copia de seguridad antes de ejecutar
- **Manejo de errores**: Registra todos los errores sin interrumpir el proceso

## 🔧 Dependencias

El script utiliza las siguientes librerías:

- `python-magic-bin`: Detección de tipos de archivo
- `chardet`: Detección de codificación de texto
- `pillow`: Análisis de metadatos de imágenes
- `PyPDF2`: Lectura de archivos PDF
- `python-docx`: Lectura de documentos Word
- `openpyxl`: Lectura de archivos Excel
- `mutagen`: Metadatos de archivos de audio

## 🎯 Casos de Uso

- **Escritorio desordenado**: Organiza automáticamente cientos de archivos
- **Descargas**: Clasifica archivos descargados por tipo
- **Proyectos**: Separa código, documentos e imágenes
- **Multimedia**: Organiza fotos, videos y música
- **Trabajo**: Clasifica documentos profesionales

## 🤝 Personalización

Puedes modificar las categorías editando el diccionario `self.categorias` en el archivo `organizador_escritorio.py`:

```python
'Nueva_Categoria': {
    'extensiones': ['.ext1', '.ext2'],
    'palabras_clave': ['palabra1', 'palabra2']
}
```

## 📞 Soporte

Si encuentras algún problema:

1. Revisa el archivo de informe generado
2. Verifica que todas las dependencias estén instaladas
3. Ejecuta con permisos de administrador si es necesario
4. Revisa los mensajes de error en la consola

## 📄 Licencia

Este proyecto es de código abierto y está disponible bajo la licencia MIT.

---

**¡Disfruta de tu escritorio organizado! 🎉**

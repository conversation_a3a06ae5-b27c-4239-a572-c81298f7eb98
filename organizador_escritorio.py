#!/usr/bin/env python3
"""
Organizador Inteligente de Escritorio
Analiza y organiza archivos del escritorio por contenido y tipo
"""

import os
import shutil
import json
import mimetypes
from pathlib import Path
from collections import defaultdict
import subprocess
import sys
from datetime import datetime

class OrganizadorEscritorio:
    def __init__(self, escritorio_path=None):
        if escritorio_path is None:
            # Detectar automáticamente la ruta del escritorio
            if os.name == 'nt':  # Windows
                escritorio_path = os.path.join(os.path.expanduser('~'), 'Desktop')
            else:  # Linux/Mac
                escritorio_path = os.path.join(os.path.expanduser('~'), 'Desktop')
        
        self.escritorio_path = Path(escritorio_path)
        self.categorias = {
            'Documentos': {
                'extensiones': ['.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt'],
                'palabras_clave': ['documento', 'texto', 'informe', 'carta', 'curriculum']
            },
            'Imagenes': {
                'extensiones': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp'],
                'palabras_clave': ['imagen', 'foto', 'picture', 'screenshot']
            },
            'Videos': {
                'extensiones': ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm'],
                'palabras_clave': ['video', 'pelicula', 'movie', 'clip']
            },
            'Audio': {
                'extensiones': ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a'],
                'palabras_clave': ['audio', 'musica', 'song', 'sound']
            },
            'Programacion': {
                'extensiones': ['.py', '.js', '.html', '.css', '.java', '.cpp', '.c', '.php', '.rb', '.go'],
                'palabras_clave': ['codigo', 'script', 'programa', 'development']
            },
            'Archivos_Comprimidos': {
                'extensiones': ['.zip', '.rar', '.7z', '.tar', '.gz'],
                'palabras_clave': ['archivo', 'comprimido', 'backup']
            },
            'Hojas_Calculo': {
                'extensiones': ['.xlsx', '.xls', '.csv', '.ods'],
                'palabras_clave': ['datos', 'tabla', 'calculo', 'spreadsheet']
            },
            'Presentaciones': {
                'extensiones': ['.pptx', '.ppt', '.odp'],
                'palabras_clave': ['presentacion', 'slides', 'diapositiva']
            }
        }
        self.informe = {
            'archivos_procesados': 0,
            'carpetas_creadas': [],
            'archivos_movidos': defaultdict(list),
            'errores': [],
            'archivos_sin_categoria': []
        }

    def instalar_dependencias(self):
        """Instala las dependencias necesarias usando mise si está disponible"""
        dependencias = [
            'python-magic-bin',  # Para análisis de tipos de archivo
            'chardet',           # Para detección de codificación
            'pillow',            # Para análisis de imágenes
            'PyPDF2',            # Para leer PDFs
            'python-docx',       # Para leer documentos Word
            'openpyxl',          # Para leer Excel
            'mutagen'            # Para metadatos de audio
        ]

        for dep in dependencias:
            try:
                # Mapear nombres de paquetes a nombres de importación
                import_name = dep.replace('-', '_').replace('python_', '').replace('PyPDF2', 'PyPDF2')
                if dep == 'python-docx':
                    import_name = 'docx'
                elif dep == 'python-magic-bin':
                    import_name = 'magic'

                __import__(import_name)
                print(f"✓ {dep} ya está instalado")
            except ImportError:
                print(f"Instalando {dep}...")
                try:
                    # Intentar con pip primero
                    subprocess.check_call([sys.executable, '-m', 'pip', 'install', dep])
                    print(f"✓ {dep} instalado con pip")
                except subprocess.CalledProcessError:
                    try:
                        # Intentar con mise si está disponible
                        subprocess.check_call(['mise', 'exec', 'python', '--', 'pip', 'install', dep])
                        print(f"✓ {dep} instalado con mise")
                    except (subprocess.CalledProcessError, FileNotFoundError):
                        print(f"⚠ No se pudo instalar {dep}")
                        self.informe['errores'].append(f"No se pudo instalar {dep}")

    def analizar_contenido_archivo(self, archivo_path):
        """Analiza el contenido de un archivo para determinar su categoría"""
        try:
            # Análisis por extensión
            extension = archivo_path.suffix.lower()

            # Análisis por tipo MIME
            mime_type, _ = mimetypes.guess_type(str(archivo_path))

            # Análisis por nombre del archivo
            nombre_archivo = archivo_path.name.lower()

            # Análisis de contenido específico por tipo
            contenido = ""
            metadatos = {}

            # Análisis de archivos de texto
            if extension in ['.txt', '.py', '.js', '.html', '.css', '.md', '.json', '.xml']:
                contenido = self._leer_archivo_texto(archivo_path)

            # Análisis de PDFs
            elif extension == '.pdf':
                contenido = self._leer_pdf(archivo_path)

            # Análisis de documentos Word
            elif extension in ['.docx', '.doc']:
                contenido = self._leer_word(archivo_path)

            # Análisis de hojas de cálculo
            elif extension in ['.xlsx', '.xls']:
                contenido, metadatos = self._leer_excel(archivo_path)

            # Análisis de archivos de audio
            elif extension in ['.mp3', '.wav', '.flac', '.m4a']:
                metadatos = self._leer_metadatos_audio(archivo_path)

            # Análisis de imágenes
            elif extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
                metadatos = self._leer_metadatos_imagen(archivo_path)

            return {
                'extension': extension,
                'mime_type': mime_type,
                'nombre': nombre_archivo,
                'contenido': contenido.lower() if contenido else "",
                'metadatos': metadatos,
                'tamaño': archivo_path.stat().st_size
            }
        except Exception as e:
            self.informe['errores'].append(f"Error analizando {archivo_path}: {str(e)}")
            return None

    def _leer_archivo_texto(self, archivo_path):
        """Lee contenido de archivos de texto"""
        try:
            # Detectar codificación
            with open(archivo_path, 'rb') as f:
                raw_data = f.read(10000)

            try:
                import chardet
                encoding = chardet.detect(raw_data)['encoding'] or 'utf-8'
            except ImportError:
                encoding = 'utf-8'

            with open(archivo_path, 'r', encoding=encoding, errors='ignore') as f:
                return f.read(2000)  # Leer primeros 2000 caracteres
        except:
            return ""

    def _leer_pdf(self, archivo_path):
        """Lee contenido de archivos PDF"""
        try:
            import PyPDF2
            with open(archivo_path, 'rb') as f:
                reader = PyPDF2.PdfReader(f)
                texto = ""
                for page in reader.pages[:3]:  # Solo primeras 3 páginas
                    texto += page.extract_text()
                return texto[:2000]
        except ImportError:
            return ""
        except:
            return ""

    def _leer_word(self, archivo_path):
        """Lee contenido de documentos Word"""
        try:
            import docx
            doc = docx.Document(archivo_path)
            texto = ""
            for paragraph in doc.paragraphs[:10]:  # Solo primeros 10 párrafos
                texto += paragraph.text + " "
            return texto[:2000]
        except ImportError:
            return ""
        except:
            return ""

    def _leer_excel(self, archivo_path):
        """Lee contenido de hojas de cálculo Excel"""
        try:
            import openpyxl
            wb = openpyxl.load_workbook(archivo_path, read_only=True)
            ws = wb.active
            contenido = ""
            metadatos = {'hojas': len(wb.sheetnames)}

            # Leer algunas celdas para análisis
            for row in ws.iter_rows(max_row=10, max_col=10, values_only=True):
                for cell in row:
                    if cell:
                        contenido += str(cell) + " "

            return contenido[:1000], metadatos
        except ImportError:
            return "", {}
        except:
            return "", {}

    def _leer_metadatos_audio(self, archivo_path):
        """Lee metadatos de archivos de audio"""
        try:
            from mutagen import File
            audio_file = File(archivo_path)
            if audio_file:
                return {
                    'duracion': getattr(audio_file.info, 'length', 0),
                    'bitrate': getattr(audio_file.info, 'bitrate', 0),
                    'artista': audio_file.get('TPE1', [''])[0] if audio_file.get('TPE1') else '',
                    'titulo': audio_file.get('TIT2', [''])[0] if audio_file.get('TIT2') else ''
                }
        except ImportError:
            pass
        except:
            pass
        return {}

    def _leer_metadatos_imagen(self, archivo_path):
        """Lee metadatos de archivos de imagen"""
        try:
            from PIL import Image
            with Image.open(archivo_path) as img:
                return {
                    'dimensiones': img.size,
                    'formato': img.format,
                    'modo': img.mode
                }
        except ImportError:
            pass
        except:
            pass
        return {}

    def determinar_categoria(self, info_archivo):
        """Determina la categoría de un archivo basado en su análisis"""
        if not info_archivo:
            return 'Sin_Categoria'

        # Puntuación por categoría
        puntuaciones = defaultdict(int)

        for categoria, criterios in self.categorias.items():
            # Puntos por extensión (peso alto)
            if info_archivo['extension'] in criterios['extensiones']:
                puntuaciones[categoria] += 15

            # Puntos por palabras clave en nombre
            for palabra in criterios['palabras_clave']:
                if palabra in info_archivo['nombre']:
                    puntuaciones[categoria] += 8
                if palabra in info_archivo['contenido']:
                    puntuaciones[categoria] += 5

            # Puntos por tipo MIME (peso alto)
            if info_archivo['mime_type']:
                if categoria == 'Imagenes' and info_archivo['mime_type'].startswith('image/'):
                    puntuaciones[categoria] += 12
                elif categoria == 'Videos' and info_archivo['mime_type'].startswith('video/'):
                    puntuaciones[categoria] += 12
                elif categoria == 'Audio' and info_archivo['mime_type'].startswith('audio/'):
                    puntuaciones[categoria] += 12
                elif categoria == 'Documentos' and any(x in info_archivo['mime_type'] for x in ['text', 'pdf', 'document']):
                    puntuaciones[categoria] += 10
                elif categoria == 'Archivos_Comprimidos' and any(x in info_archivo['mime_type'] for x in ['zip', 'compressed']):
                    puntuaciones[categoria] += 12

        # Análisis adicional basado en metadatos
        metadatos = info_archivo.get('metadatos', {})

        # Para archivos de audio
        if metadatos.get('duracion', 0) > 0 or metadatos.get('artista'):
            puntuaciones['Audio'] += 10

        # Para imágenes
        if metadatos.get('dimensiones'):
            puntuaciones['Imagenes'] += 10

        # Para hojas de cálculo
        if metadatos.get('hojas', 0) > 0:
            puntuaciones['Hojas_Calculo'] += 10

        # Análisis de contenido específico
        contenido = info_archivo['contenido']
        if contenido:
            # Detectar código de programación
            indicadores_codigo = ['def ', 'function', 'class ', 'import ', '#include', 'var ', 'const ', 'let ']
            if any(indicador in contenido for indicador in indicadores_codigo):
                puntuaciones['Programacion'] += 8

            # Detectar documentos académicos/profesionales
            indicadores_documento = ['resumen', 'abstract', 'introducción', 'conclusión', 'referencias', 'bibliography']
            if any(indicador in contenido for indicador in indicadores_documento):
                puntuaciones['Documentos'] += 6

        # Análisis por tamaño (heurística adicional)
        tamaño = info_archivo['tamaño']
        if tamaño > 100 * 1024 * 1024:  # > 100MB
            if info_archivo['extension'] in ['.mp4', '.avi', '.mkv']:
                puntuaciones['Videos'] += 5
            elif info_archivo['extension'] in ['.zip', '.rar', '.7z']:
                puntuaciones['Archivos_Comprimidos'] += 5

        # Retornar la categoría con mayor puntuación
        if puntuaciones:
            categoria_elegida = max(puntuaciones, key=puntuaciones.get)
            # Solo asignar si la puntuación es significativa
            if puntuaciones[categoria_elegida] >= 5:
                return categoria_elegida

        return 'Sin_Categoria'

    def crear_carpeta_categoria(self, categoria):
        """Crea una carpeta para la categoría si no existe"""
        carpeta_path = self.escritorio_path / categoria
        if not carpeta_path.exists():
            carpeta_path.mkdir(exist_ok=True)
            self.informe['carpetas_creadas'].append(str(carpeta_path))
            print(f"📁 Carpeta creada: {categoria}")
        return carpeta_path

    def mover_archivo(self, archivo_path, categoria):
        """Mueve un archivo a la carpeta de su categoría"""
        try:
            carpeta_destino = self.crear_carpeta_categoria(categoria)
            destino_path = carpeta_destino / archivo_path.name
            
            # Si ya existe un archivo con el mismo nombre, agregar número
            contador = 1
            while destino_path.exists():
                nombre_base = archivo_path.stem
                extension = archivo_path.suffix
                destino_path = carpeta_destino / f"{nombre_base}_{contador}{extension}"
                contador += 1
            
            shutil.move(str(archivo_path), str(destino_path))
            self.informe['archivos_movidos'][categoria].append({
                'origen': str(archivo_path),
                'destino': str(destino_path)
            })
            print(f"📄 Movido: {archivo_path.name} → {categoria}")
            return True
            
        except Exception as e:
            self.informe['errores'].append(f"Error moviendo {archivo_path}: {str(e)}")
            return False

    def organizar_escritorio(self):
        """Función principal que organiza todos los archivos del escritorio"""
        print(f"🚀 Iniciando organización del escritorio: {self.escritorio_path}")
        print("=" * 60)
        
        # Instalar dependencias
        self.instalar_dependencias()
        
        if not self.escritorio_path.exists():
            print(f"❌ Error: No se encontró el escritorio en {self.escritorio_path}")
            return
        
        # Obtener todos los archivos (no carpetas) del escritorio
        archivos = [f for f in self.escritorio_path.iterdir() if f.is_file()]
        
        if not archivos:
            print("📭 No se encontraron archivos para organizar")
            return
        
        print(f"📊 Encontrados {len(archivos)} archivos para organizar")
        print("-" * 60)
        
        # Procesar cada archivo
        for archivo in archivos:
            print(f"🔍 Analizando: {archivo.name}")
            
            # Analizar contenido
            info_archivo = self.analizar_contenido_archivo(archivo)
            
            # Determinar categoría
            categoria = self.determinar_categoria(info_archivo)
            
            if categoria == 'Sin_Categoria':
                self.informe['archivos_sin_categoria'].append(str(archivo))
                print(f"❓ Sin categoría definida: {archivo.name}")
            else:
                # Mover archivo
                if self.mover_archivo(archivo, categoria):
                    self.informe['archivos_procesados'] += 1
        
        # Generar informe final
        self.generar_informe()

    def generar_informe(self):
        """Genera un informe detallado de la organización"""
        print("\n" + "=" * 60)
        print("📋 INFORME FINAL DE ORGANIZACIÓN")
        print("=" * 60)
        
        print(f"✅ Archivos procesados: {self.informe['archivos_procesados']}")
        print(f"📁 Carpetas creadas: {len(self.informe['carpetas_creadas'])}")
        
        if self.informe['carpetas_creadas']:
            for carpeta in self.informe['carpetas_creadas']:
                print(f"   • {Path(carpeta).name}")
        
        print("\n📊 DISTRIBUCIÓN POR CATEGORÍAS:")
        for categoria, archivos in self.informe['archivos_movidos'].items():
            print(f"   📂 {categoria}: {len(archivos)} archivos")
            for archivo in archivos[:3]:  # Mostrar solo los primeros 3
                print(f"      • {Path(archivo['origen']).name}")
            if len(archivos) > 3:
                print(f"      ... y {len(archivos) - 3} más")
        
        if self.informe['archivos_sin_categoria']:
            print(f"\n❓ Archivos sin categoría: {len(self.informe['archivos_sin_categoria'])}")
            for archivo in self.informe['archivos_sin_categoria'][:5]:
                print(f"   • {Path(archivo).name}")
        
        if self.informe['errores']:
            print(f"\n⚠ Errores encontrados: {len(self.informe['errores'])}")
            for error in self.informe['errores'][:3]:
                print(f"   • {error}")
        
        # Guardar informe en JSON
        informe_path = self.escritorio_path / f"informe_organizacion_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(informe_path, 'w', encoding='utf-8') as f:
            json.dump(self.informe, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Informe detallado guardado en: {informe_path.name}")
        print("🎉 ¡Organización completada!")

def main():
    """Función principal"""
    print("🏠 ORGANIZADOR INTELIGENTE DE ESCRITORIO")
    print("=" * 60)
    
    # Permitir especificar ruta personalizada
    if len(sys.argv) > 1:
        escritorio_path = sys.argv[1]
    else:
        escritorio_path = None
    
    organizador = OrganizadorEscritorio(escritorio_path)
    organizador.organizar_escritorio()

if __name__ == "__main__":
    main()

@echo off
chcp 65001 >nul
title Organizador Inteligente de Escritorio

echo.
echo 🏠 ORGANIZADOR INTELIGENTE DE ESCRITORIO
echo ========================================
echo.

REM Verificar si Python está instalado
echo 🔍 Verificando Python...

REM Intentar con python
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Python encontrado
    set PYTHON_CMD=python
    goto :run
)

REM Intentar con py
py --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Python encontrado
    set PYTHON_CMD=py
    goto :run
)

REM Intentar con python3
python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Python encontrado
    set PYTHON_CMD=python3
    goto :run
)

REM Python no encontrado
echo.
echo ❌ Python no encontrado
echo.
echo 🚨 NECESITAS INSTALAR PYTHON PRIMERO
echo ====================================
echo.
echo Por favor ejecuta "INSTALAR.bat" primero para instalar
echo Python y todas las dependencias necesarias.
echo.
echo ¿Quieres ejecutar el instalador ahora? (S/N)
set /p choice="Tu elección: "
if /i "%choice%"=="s" (
    call INSTALAR.bat
    exit /b
)
echo.
pause
exit /b 1

:run
echo.
echo 🚀 Iniciando organizador...
echo.

REM Verificar si el script principal existe
if not exist "organizador_escritorio.py" (
    echo ❌ Error: No se encontró organizador_escritorio.py
    echo.
    echo Asegúrate de que todos los archivos estén en la misma carpeta:
    echo • organizador_escritorio.py
    echo • EJECUTAR_ORGANIZADOR.bat
    echo • INSTALAR.bat
    echo.
    pause
    exit /b 1
)

REM Ejecutar el organizador
%PYTHON_CMD% organizador_escritorio.py

echo.
echo ✨ Proceso completado
echo.
echo 📋 Revisa el informe generado para ver los detalles
echo    de la organización realizada.
echo.
pause

#!/usr/bin/env python3
"""
Script de instalación para el Organizador de Escritorio
Instala todas las dependencias necesarias y configura el entorno
"""

import subprocess
import sys
import os
from pathlib import Path

def verificar_python():
    """Verifica que Python esté instalado y sea una versión compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Error: Se requiere Python 3.7 o superior")
        print(f"   Versión actual: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} detectado")
    return True

def verificar_mise():
    """Verifica si mise está disponible"""
    try:
        result = subprocess.run(['mise', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ mise detectado: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    
    print("⚠️  mise no detectado - se usará pip directamente")
    return False

def instalar_dependencias():
    """Instala todas las dependencias necesarias"""
    dependencias = [
        'python-magic-bin',  # Para análisis de tipos de archivo
        'chardet',           # Para detección de codificación
        'pillow',            # Para análisis de imágenes
        'PyPDF2',            # Para leer PDFs
        'python-docx',       # Para leer documentos Word
        'openpyxl',          # Para leer Excel
        'mutagen'            # Para metadatos de audio
    ]
    
    print("📦 Instalando dependencias...")
    print("-" * 50)
    
    mise_disponible = verificar_mise()
    errores = []
    
    for dep in dependencias:
        print(f"Instalando {dep}...")
        
        try:
            if mise_disponible:
                # Intentar con mise primero
                result = subprocess.run(
                    ['mise', 'exec', 'python', '--', '-m', 'pip', 'install', dep],
                    capture_output=True,
                    text=True
                )
                if result.returncode != 0:
                    raise subprocess.CalledProcessError(result.returncode, 'mise')
            else:
                # Usar pip directamente
                result = subprocess.run(
                    [sys.executable, '-m', 'pip', 'install', dep],
                    capture_output=True,
                    text=True
                )
                if result.returncode != 0:
                    raise subprocess.CalledProcessError(result.returncode, 'pip')
            
            print(f"✅ {dep} instalado correctamente")
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Error instalando {dep}")
            errores.append(dep)
            if result.stderr:
                print(f"   Error: {result.stderr.strip()}")
    
    if errores:
        print(f"\n⚠️  No se pudieron instalar: {', '.join(errores)}")
        print("   El organizador funcionará con funcionalidad limitada")
    else:
        print("\n🎉 Todas las dependencias instaladas correctamente")
    
    return len(errores) == 0

def crear_script_ejecutable():
    """Crea un script ejecutable para facilitar el uso"""
    script_content = '''@echo off
echo 🏠 ORGANIZADOR INTELIGENTE DE ESCRITORIO
echo =====================================
echo.
python "%~dp0organizador_escritorio.py" %*
pause
'''
    
    script_path = Path('ejecutar_organizador.bat')
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"✅ Script ejecutable creado: {script_path}")
    return script_path

def mostrar_instrucciones():
    """Muestra las instrucciones de uso"""
    print("\n" + "=" * 60)
    print("📋 INSTRUCCIONES DE USO")
    print("=" * 60)
    print()
    print("1. 🖱️  OPCIÓN FÁCIL:")
    print("   • Haz doble clic en 'ejecutar_organizador.bat'")
    print("   • El script organizará automáticamente tu escritorio")
    print()
    print("2. 🖥️  OPCIÓN LÍNEA DE COMANDOS:")
    print("   • Abre una terminal en esta carpeta")
    print("   • Ejecuta: python organizador_escritorio.py")
    print()
    print("3. 📁 CARPETA PERSONALIZADA:")
    print("   • python organizador_escritorio.py \"C:\\ruta\\a\\carpeta\"")
    print()
    print("🔍 QUÉ HACE EL ORGANIZADOR:")
    print("   • Analiza todos los archivos de tu escritorio")
    print("   • Los clasifica por tipo y contenido")
    print("   • Los mueve a carpetas organizadas")
    print("   • Genera un informe detallado")
    print()
    print("📂 CATEGORÍAS CREADAS:")
    categorias = [
        "📄 Documentos", "🖼️  Imagenes", "🎬 Videos", "🎵 Audio",
        "💻 Programacion", "📦 Archivos_Comprimidos", 
        "📊 Hojas_Calculo", "📽️  Presentaciones"
    ]
    for categoria in categorias:
        print(f"   • {categoria}")
    print()
    print("⚠️  IMPORTANTE:")
    print("   • Haz una copia de seguridad antes de ejecutar")
    print("   • El script NO elimina archivos, solo los mueve")
    print("   • Revisa el informe generado al final")
    print()

def mostrar_instalacion_python():
    """Muestra instrucciones para instalar Python"""
    print("\n" + "=" * 60)
    print("🐍 PYTHON NO ENCONTRADO")
    print("=" * 60)
    print()
    print("Para usar este organizador necesitas instalar Python primero.")
    print()
    print("📥 OPCIONES DE INSTALACIÓN:")
    print()
    print("1. 🏪 MICROSOFT STORE (Recomendado para Windows 10/11):")
    print("   • Abre Microsoft Store")
    print("   • Busca 'Python'")
    print("   • Instala 'Python 3.11' o superior")
    print()
    print("2. 🌐 SITIO WEB OFICIAL:")
    print("   • Ve a: https://www.python.org/downloads/")
    print("   • Descarga Python 3.11 o superior")
    print("   • Ejecuta el instalador")
    print("   • ¡IMPORTANTE! Marca 'Add Python to PATH'")
    print()
    print("3. 📦 CHOCOLATEY (si lo tienes instalado):")
    print("   • choco install python")
    print()
    print("Después de instalar Python, ejecuta este script nuevamente.")
    print()

def main():
    """Función principal de instalación"""
    print("🚀 INSTALADOR DEL ORGANIZADOR DE ESCRITORIO")
    print("=" * 60)
    print()

    # Verificar Python
    if not verificar_python():
        mostrar_instalacion_python()
        input("Presiona Enter para salir...")
        return

    print()

    # Instalar dependencias
    exito = instalar_dependencias()

    print()

    # Crear script ejecutable
    crear_script_ejecutable()

    # Mostrar instrucciones
    mostrar_instrucciones()

    if exito:
        print("🎉 ¡Instalación completada exitosamente!")
    else:
        print("⚠️  Instalación completada con advertencias")

    print("\n" + "=" * 60)
    input("Presiona Enter para continuar...")

if __name__ == "__main__":
    main()

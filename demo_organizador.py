#!/usr/bin/env python3
"""
Demo del Organizador de Escritorio
Versión simplificada que funciona sin dependencias externas
"""

import os
import shutil
import json
import mimetypes
from pathlib import Path
from collections import defaultdict
from datetime import datetime

class DemoOrganizador:
    def __init__(self, carpeta_demo="demo_escritorio"):
        self.carpeta_demo = Path(carpeta_demo)
        self.categorias = {
            'Documentos': ['.pdf', '.doc', '.docx', '.txt', '.rtf'],
            'Imagenes': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg'],
            'Videos': ['.mp4', '.avi', '.mkv', '.mov', '.wmv'],
            'Audio': ['.mp3', '.wav', '.flac', '.aac', '.ogg'],
            'Programacion': ['.py', '.js', '.html', '.css', '.java', '.cpp'],
            'Archivos_Comprimidos': ['.zip', '.rar', '.7z', '.tar', '.gz'],
            'Hojas_Calculo': ['.xlsx', '.xls', '.csv'],
            'Presentaciones': ['.pptx', '.ppt']
        }
        self.informe = {
            'archivos_procesados': 0,
            'carpetas_creadas': [],
            'archivos_movidos': defaultdict(list),
            'archivos_creados': []
        }

    def crear_archivos_demo(self):
        """Crea archivos de demostración para mostrar el funcionamiento"""
        print("📁 Creando carpeta de demostración...")
        
        # Crear carpeta demo
        if self.carpeta_demo.exists():
            shutil.rmtree(self.carpeta_demo)
        self.carpeta_demo.mkdir()
        
        # Archivos de demostración
        archivos_demo = [
            # Documentos
            ("Informe_Anual_2024.txt", "Este es un informe anual con datos importantes de la empresa."),
            ("Curriculum_Vitae.txt", "Curriculum vitae de Juan Pérez con experiencia en programación."),
            ("Manual_Usuario.txt", "Manual de usuario para el software de gestión."),
            
            # Programación
            ("script_backup.py", "#!/usr/bin/env python3\n# Script de backup\nimport os\nprint('Backup iniciado')"),
            ("index.html", "<!DOCTYPE html>\n<html>\n<head><title>Mi Web</title></head>\n<body><h1>Hola Mundo</h1></body></html>"),
            ("styles.css", "body { font-family: Arial; background-color: #f0f0f0; }"),
            
            # Datos
            ("ventas_2024.csv", "Mes,Ventas,Región\nEnero,15000,Norte\nFebrero,18000,Sur"),
            ("presupuesto.xlsx", "Archivo Excel simulado"),
            
            # Multimedia (archivos vacíos para demo)
            ("foto_vacaciones.jpg", ""),
            ("video_presentacion.mp4", ""),
            ("cancion_favorita.mp3", ""),
            
            # Comprimidos
            ("backup_documentos.zip", ""),
            ("proyecto_completo.rar", ""),
            
            # Sin categoría
            ("archivo_desconocido.xyz", "Contenido de archivo con extensión desconocida"),
        ]
        
        print("📄 Creando archivos de demostración...")
        for nombre, contenido in archivos_demo:
            archivo_path = self.carpeta_demo / nombre
            with open(archivo_path, 'w', encoding='utf-8') as f:
                f.write(contenido)
            self.informe['archivos_creados'].append(str(archivo_path))
            print(f"   ✓ {nombre}")
        
        print(f"\n✅ Creados {len(archivos_demo)} archivos de demostración en '{self.carpeta_demo}'")

    def determinar_categoria(self, archivo_path):
        """Determina la categoría de un archivo"""
        extension = archivo_path.suffix.lower()
        
        for categoria, extensiones in self.categorias.items():
            if extension in extensiones:
                return categoria
        
        return 'Sin_Categoria'

    def organizar_demo(self):
        """Organiza los archivos de demostración"""
        print(f"\n🚀 Organizando archivos en '{self.carpeta_demo}'...")
        print("=" * 50)
        
        # Obtener archivos
        archivos = [f for f in self.carpeta_demo.iterdir() if f.is_file()]
        
        for archivo in archivos:
            print(f"\n🔍 Analizando: {archivo.name}")
            
            # Determinar categoría
            categoria = self.determinar_categoria(archivo)
            print(f"   📂 Categoría: {categoria}")
            
            # Crear carpeta si no existe
            carpeta_categoria = self.carpeta_demo / categoria
            if not carpeta_categoria.exists():
                carpeta_categoria.mkdir()
                self.informe['carpetas_creadas'].append(str(carpeta_categoria))
                print(f"   📁 Carpeta creada: {categoria}")
            
            # Mover archivo
            destino = carpeta_categoria / archivo.name
            shutil.move(str(archivo), str(destino))
            self.informe['archivos_movidos'][categoria].append({
                'origen': str(archivo),
                'destino': str(destino)
            })
            self.informe['archivos_procesados'] += 1
            print(f"   ✅ Movido a: {categoria}/{archivo.name}")

    def generar_informe_demo(self):
        """Genera el informe de la demostración"""
        print("\n" + "=" * 50)
        print("📋 INFORME DE DEMOSTRACIÓN")
        print("=" * 50)
        
        print(f"✅ Archivos procesados: {self.informe['archivos_procesados']}")
        print(f"📁 Carpetas creadas: {len(self.informe['carpetas_creadas'])}")
        
        print("\n📊 DISTRIBUCIÓN POR CATEGORÍAS:")
        for categoria, archivos in self.informe['archivos_movidos'].items():
            print(f"   📂 {categoria}: {len(archivos)} archivos")
            for archivo in archivos:
                nombre = Path(archivo['origen']).name
                print(f"      • {nombre}")
        
        # Guardar informe
        informe_path = self.carpeta_demo / f"informe_demo_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(informe_path, 'w', encoding='utf-8') as f:
            json.dump(self.informe, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Informe guardado en: {informe_path.name}")

    def mostrar_estructura_final(self):
        """Muestra la estructura final de carpetas"""
        print("\n📁 ESTRUCTURA FINAL:")
        print("=" * 30)
        
        def mostrar_arbol(path, prefijo=""):
            items = sorted(path.iterdir())
            for i, item in enumerate(items):
                es_ultimo = i == len(items) - 1
                simbolo = "└── " if es_ultimo else "├── "
                
                if item.is_dir():
                    print(f"{prefijo}{simbolo}📁 {item.name}/")
                    nuevo_prefijo = prefijo + ("    " if es_ultimo else "│   ")
                    mostrar_arbol(item, nuevo_prefijo)
                else:
                    print(f"{prefijo}{simbolo}📄 {item.name}")
        
        mostrar_arbol(self.carpeta_demo)

def main():
    """Función principal de la demostración"""
    print("🎬 DEMOSTRACIÓN DEL ORGANIZADOR DE ESCRITORIO")
    print("=" * 60)
    print()
    print("Esta demostración creará archivos de ejemplo y los organizará")
    print("para mostrar cómo funciona el organizador real.")
    print()

    # Verificar si se ejecuta de forma interactiva
    import sys
    interactivo = sys.stdin.isatty()

    if interactivo:
        input("Presiona Enter para continuar...")
    else:
        print("Ejecutando demostración automáticamente...")
    print()

    # Crear y ejecutar demo
    demo = DemoOrganizador()

    # Paso 1: Crear archivos
    demo.crear_archivos_demo()

    if interactivo:
        input("\nPresiona Enter para organizar los archivos...")
    else:
        print("\nOrganizando archivos automáticamente...")

    # Paso 2: Organizar
    demo.organizar_demo()

    # Paso 3: Mostrar informe
    demo.generar_informe_demo()

    # Paso 4: Mostrar estructura
    demo.mostrar_estructura_final()

    print("\n🎉 ¡Demostración completada!")
    print()
    print("💡 PRÓXIMOS PASOS:")
    print("   1. Revisa la carpeta 'demo_escritorio' creada")
    print("   2. Ejecuta 'INSTALAR.bat' para instalar el organizador completo")
    print("   3. Usa 'EJECUTAR_ORGANIZADOR.bat' para organizar tu escritorio real")
    print()

    if interactivo:
        input("Presiona Enter para salir...")
    else:
        print("Demostración completada. Revisa la carpeta 'demo_escritorio'.")

if __name__ == "__main__":
    main()

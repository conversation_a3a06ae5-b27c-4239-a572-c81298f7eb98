@echo off
chcp 65001 >nul
title Instalador del Organizador de Escritorio

echo.
echo 🚀 INSTALADOR DEL ORGANIZADOR DE ESCRITORIO
echo ============================================
echo.

REM Verificar si Python está instalado
echo 🔍 Verificando Python...

REM Intentar con python
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Python encontrado con comando 'python'
    set PYTHON_CMD=python
    goto :install
)

REM Intentar con py
py --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Python encontrado con comando 'py'
    set PYTHON_CMD=py
    goto :install
)

REM Intentar con python3
python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Python encontrado con comando 'python3'
    set PYTHON_CMD=python3
    goto :install
)

REM Python no encontrado
echo.
echo ❌ Python no encontrado en el sistema
echo.
echo 🐍 NECESITAS INSTALAR PYTHON PRIMERO
echo ====================================
echo.
echo 📥 OPCIONES DE INSTALACIÓN:
echo.
echo 1. 🏪 MICROSOFT STORE (Recomendado):
echo    • Presiona Win + S y busca "Microsoft Store"
echo    • Busca "Python" e instala Python 3.11 o superior
echo.
echo 2. 🌐 SITIO WEB OFICIAL:
echo    • Ve a: https://www.python.org/downloads/
echo    • Descarga Python 3.11 o superior
echo    • Al instalar, marca "Add Python to PATH"
echo.
echo 3. 🔗 ABRIR MICROSOFT STORE AHORA:
echo    • Presiona 'S' para abrir Microsoft Store
echo    • Presiona cualquier otra tecla para salir
echo.
set /p choice="Tu elección: "
if /i "%choice%"=="s" (
    start ms-windows-store://search/?query=python
    echo.
    echo 📱 Microsoft Store abierto. Instala Python y ejecuta este script nuevamente.
)
echo.
pause
exit /b 1

:install
echo.
echo 📦 Ejecutando instalador de Python...
echo.
%PYTHON_CMD% instalar_organizador.py

if %errorlevel% == 0 (
    echo.
    echo 🎉 ¡Instalación completada!
    echo.
    echo 🚀 PRÓXIMOS PASOS:
    echo ==================
    echo.
    echo 1. Haz doble clic en "EJECUTAR_ORGANIZADOR.bat"
    echo 2. O ejecuta: %PYTHON_CMD% organizador_escritorio.py
    echo.
) else (
    echo.
    echo ⚠️  Hubo algunos problemas durante la instalación
    echo    Revisa los mensajes anteriores para más detalles
    echo.
)

echo.
pause
